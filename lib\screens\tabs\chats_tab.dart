import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class ChatsTab extends StatefulWidget {
  const ChatsTab({super.key});

  @override
  State<ChatsTab> createState() => _ChatsTabState();
}

class _ChatsTabState extends State<ChatsTab> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          // Search Bar
          _buildSearchBar(),

          // Chat List
          Expanded(
            child: RefreshIndicator(
              onRefresh: () async {
                // TODO: Implement refresh logic
                await Future.delayed(const Duration(seconds: 1));
              },
              child: _buildChatList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search conversations...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(25),
                  borderSide: BorderSide.none,
                ),
                filled: true,
                fillColor: Colors.grey.shade100,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  _searchQuery = value.toLowerCase();
                });
              },
            ),
          ),
          const SizedBox(width: 12),
          FloatingActionButton(
            mini: true,
            onPressed: _showNewChatDialog,
            backgroundColor: const Color(0xFF2E7D32),
            child: const Icon(Icons.add, color: Colors.white),
          ),
        ],
      ),
    );
  }

  Widget _buildChatList() {
    final chats = _getFilteredChats();

    if (chats.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      itemCount: chats.length,
      itemBuilder: (context, index) {
        final chat = chats[index];
        return _buildChatTile(chat);
      },
    );
  }

  Widget _buildChatTile(Map<String, dynamic> chat) {
    final isOnline = chat['isOnline'] as bool;
    final hasUnread = chat['unreadCount'] as int > 0;
    final lastMessageTime = chat['lastMessageTime'] as DateTime;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      child: Card(
        elevation: hasUnread ? 2 : 1,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        child: ListTile(
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          leading: Stack(
            children: [
              CircleAvatar(
                radius: 28,
                backgroundColor: const Color(0xFF2E7D32),
                backgroundImage: chat['avatar'] != null
                    ? NetworkImage(chat['avatar'])
                    : null,
                child: chat['avatar'] == null
                    ? Text(
                        chat['name'][0].toUpperCase(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      )
                    : null,
              ),
              if (isOnline)
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      color: Colors.green,
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 2),
                    ),
                  ),
                ),
            ],
          ),
          title: Row(
            children: [
              Expanded(
                child: Text(
                  chat['name'],
                  style: TextStyle(
                    fontWeight: hasUnread ? FontWeight.bold : FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
              Text(
                _formatTime(lastMessageTime),
                style: TextStyle(
                  color: hasUnread ? const Color(0xFF2E7D32) : Colors.grey,
                  fontSize: 12,
                  fontWeight: hasUnread ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
          subtitle: Row(
            children: [
              if (chat['isTyping'])
                const Expanded(
                  child: Text(
                    'typing...',
                    style: TextStyle(
                      color: Color(0xFF2E7D32),
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                )
              else
                Expanded(
                  child: Text(
                    chat['lastMessage'],
                    style: TextStyle(
                      color: hasUnread ? Colors.black87 : Colors.grey.shade600,
                      fontWeight: hasUnread
                          ? FontWeight.w500
                          : FontWeight.normal,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              if (hasUnread)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFF2E7D32),
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Text(
                    chat['unreadCount'].toString(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          onTap: () => _openChat(chat),
          onLongPress: () => _showChatOptions(chat),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 80,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 16),
          Text(
            _searchQuery.isNotEmpty
                ? 'No chats found for "$_searchQuery"'
                : 'No conversations yet',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _searchQuery.isNotEmpty
                ? 'Try searching with different keywords'
                : 'Start a conversation by tapping the + button',
            style: TextStyle(color: Colors.grey.shade500),
            textAlign: TextAlign.center,
          ),
          if (_searchQuery.isEmpty) ...[
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _showNewChatDialog,
              icon: const Icon(Icons.add),
              label: const Text('Start New Chat'),
            ),
          ],
        ],
      ),
    );
  }

  List<Map<String, dynamic>> _getFilteredChats() {
    final sampleChats = _generateSampleChats();

    if (_searchQuery.isEmpty) {
      return sampleChats;
    }

    return sampleChats.where((chat) {
      return chat['name'].toLowerCase().contains(_searchQuery) ||
          chat['lastMessage'].toLowerCase().contains(_searchQuery);
    }).toList();
  }

  List<Map<String, dynamic>> _generateSampleChats() {
    return [
      {
        'id': '1',
        'name': 'John Doe',
        'avatar': null,
        'lastMessage': 'Is the laptop still available?',
        'lastMessageTime': DateTime.now().subtract(const Duration(minutes: 5)),
        'unreadCount': 2,
        'isOnline': true,
        'isTyping': false,
      },
      {
        'id': '2',
        'name': 'Jane Smith',
        'avatar': null,
        'lastMessage': 'Thanks for the quick delivery!',
        'lastMessageTime': DateTime.now().subtract(const Duration(hours: 1)),
        'unreadCount': 0,
        'isOnline': false,
        'isTyping': false,
      },
      {
        'id': '3',
        'name': 'Mike Johnson',
        'avatar': null,
        'lastMessage': 'Can we meet at the library?',
        'lastMessageTime': DateTime.now().subtract(const Duration(hours: 3)),
        'unreadCount': 1,
        'isOnline': true,
        'isTyping': true,
      },
      {
        'id': '4',
        'name': 'Sarah Wilson',
        'avatar': null,
        'lastMessage': 'The books are in great condition',
        'lastMessageTime': DateTime.now().subtract(const Duration(days: 1)),
        'unreadCount': 0,
        'isOnline': false,
        'isTyping': false,
      },
      {
        'id': '5',
        'name': 'David Brown',
        'avatar': null,
        'lastMessage': 'Payment sent via M-Pesa',
        'lastMessageTime': DateTime.now().subtract(const Duration(days: 2)),
        'unreadCount': 0,
        'isOnline': true,
        'isTyping': false,
      },
    ];
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);

    if (difference.inMinutes < 1) {
      return 'now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m';
    } else if (difference.inDays < 1) {
      return DateFormat('HH:mm').format(time);
    } else if (difference.inDays < 7) {
      return DateFormat('EEE').format(time);
    } else {
      return DateFormat('dd/MM').format(time);
    }
  }

  void _openChat(Map<String, dynamic> chat) {
    // TODO: Navigate to chat screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Opening chat with ${chat['name']}...'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _showChatOptions(Map<String, dynamic> chat) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.mark_chat_read),
              title: const Text('Mark as Read'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Mark chat as read
              },
            ),
            ListTile(
              leading: const Icon(Icons.notifications_off),
              title: const Text('Mute Notifications'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Mute chat
              },
            ),
            ListTile(
              leading: const Icon(Icons.delete, color: Colors.red),
              title: const Text(
                'Delete Chat',
                style: TextStyle(color: Colors.red),
              ),
              onTap: () {
                Navigator.pop(context);
                // TODO: Delete chat
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showNewChatDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Start New Chat'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: InputDecoration(
                labelText: 'Search users...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16),
            Text(
              'Search for DeKUT students, faculty, or staff to start a conversation.',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement user search and chat creation
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('User search coming soon!')),
              );
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }
}
