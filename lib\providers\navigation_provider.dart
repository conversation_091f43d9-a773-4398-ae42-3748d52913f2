import 'package:flutter/material.dart';

class NavigationProvider extends ChangeNotifier {
  int _currentIndex = 0;

  int get currentIndex => _currentIndex;

  void setCurrentIndex(int index) {
    if (index != 2) { // Skip the "Add" tab (index 2) as it shows bottom sheet
      _currentIndex = index;
      notifyListeners();
    }
  }

  void showAddBottomSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const AddBottomSheet(),
    );
  }
}

class AddBottomSheet extends StatelessWidget {
  const AddBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: Colors.grey[300],
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const Padding(
            padding: EdgeInsets.all(16.0),
            child: Text(
              'What would you like to do?',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          ListTile(
            leading: const Icon(Icons.shopping_bag, color: Colors.green),
            title: const Text('Post a Product'),
            subtitle: const Text('Sell something to the community'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to add product screen
            },
          ),
          ListTile(
            leading: const Icon(Icons.forum, color: Colors.blue),
            title: const Text('Create Community Post'),
            subtitle: const Text('Share with the community'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to create post screen
            },
          ),
          ListTile(
            leading: const Icon(Icons.chat, color: Colors.orange),
            title: const Text('Start a New Chat'),
            subtitle: const Text('Message someone'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to new chat screen
            },
          ),
          ListTile(
            leading: const Icon(Icons.report, color: Colors.red),
            title: const Text('Report an Issue'),
            subtitle: const Text('Get help or report a problem'),
            onTap: () {
              Navigator.pop(context);
              // TODO: Navigate to report screen
            },
          ),
          const SizedBox(height: 20),
        ],
      ),
    );
  }
}
