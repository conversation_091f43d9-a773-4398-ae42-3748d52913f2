import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/navigation_provider.dart';
import '../providers/auth_provider.dart';
import '../widgets/custom_app_bar.dart';
import '../widgets/profile_drawer.dart';
import 'tabs/home_tab.dart';
import 'tabs/products_tab.dart';
import 'tabs/chats_tab.dart';
import 'tabs/community_tab.dart';
import 'auth/login_screen.dart';

class MainScreen extends StatelessWidget {
  const MainScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        if (!authProvider.isAuthenticated) {
          return const LoginScreen();
        }

        return Consumer<NavigationProvider>(
          builder: (context, navigationProvider, child) {
            return Scaffold(
              appBar: const CustomAppBar(),
              endDrawer: const ProfileDrawer(),
              body: IndexedStack(
                index: navigationProvider.currentIndex,
                children: const [
                  HomeTab(),
                  ProductsTab(),
                  SizedBox.shrink(), // Placeholder for Add tab (shows bottom sheet)
                  ChatsTab(),
                  CommunityTab(),
                ],
              ),
              bottomNavigationBar: BottomNavigationBar(
                type: BottomNavigationBarType.fixed,
                currentIndex: navigationProvider.currentIndex,
                onTap: (index) {
                  if (index == 2) {
                    // Show Add bottom sheet instead of navigating
                    navigationProvider.showAddBottomSheet(context);
                  } else {
                    navigationProvider.setCurrentIndex(index);
                  }
                },
                items: const [
                  BottomNavigationBarItem(
                    icon: Icon(Icons.home),
                    label: 'Home',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.shopping_bag),
                    label: 'Products',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.add_circle),
                    label: 'Add',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.chat_bubble),
                    label: 'Chats',
                  ),
                  BottomNavigationBarItem(
                    icon: Icon(Icons.group),
                    label: 'Community',
                  ),
                ],
              ),
            );
          },
        );
      },
    );
  }
}
