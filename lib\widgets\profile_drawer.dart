import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/auth_provider.dart';

class ProfileDrawer extends StatelessWidget {
  const ProfileDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        final user = authProvider.user;
        
        return Drawer(
          width: MediaQuery.of(context).size.width * 0.8,
          child: SafeArea(
            child: Column(
              children: [
                // Header with user info
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: const BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Color(0xFF2E7D32), Color(0xFF4CAF50)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Column(
                    children: [
                      CircleAvatar(
                        radius: 40,
                        backgroundColor: Colors.white,
                        backgroundImage: user?.photoURL != null
                            ? NetworkImage(user!.photoURL!)
                            : null,
                        child: user?.photoURL == null
                            ? Text(
                                user?.displayName?.isNotEmpty == true
                                    ? user!.displayName![0].toUpperCase()
                                    : user?.email?[0].toUpperCase() ?? 'U',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Color(0xFF2E7D32),
                                ),
                              )
                            : null,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        user?.displayName ?? 'DeKUT Student',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        user?.email ?? '',
                        style: const TextStyle(
                          color: Colors.white70,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white24,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Text(
                          'DeKUT Student',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Menu items
                Expanded(
                  child: ListView(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    children: [
                      _buildMenuItem(
                        icon: Icons.person,
                        title: 'My Profile',
                        subtitle: 'Edit details, view listings',
                        onTap: () {
                          Navigator.pop(context);
                          // TODO: Navigate to profile screen
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Profile screen coming soon!')),
                          );
                        },
                      ),
                      _buildMenuItem(
                        icon: Icons.shopping_bag,
                        title: 'My Listings',
                        subtitle: 'Manage your products',
                        onTap: () {
                          Navigator.pop(context);
                          // TODO: Navigate to my listings
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('My listings coming soon!')),
                          );
                        },
                      ),
                      _buildMenuItem(
                        icon: Icons.favorite,
                        title: 'Saved Items',
                        subtitle: 'Your favorite products',
                        onTap: () {
                          Navigator.pop(context);
                          // TODO: Navigate to saved items
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Saved items coming soon!')),
                          );
                        },
                      ),
                      _buildMenuItem(
                        icon: Icons.account_balance_wallet,
                        title: 'Wallet & Transactions',
                        subtitle: 'View purchase history',
                        onTap: () {
                          Navigator.pop(context);
                          // TODO: Navigate to wallet
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Wallet coming soon!')),
                          );
                        },
                      ),
                      const Divider(),
                      _buildMenuItem(
                        icon: Icons.settings,
                        title: 'Settings',
                        subtitle: 'Notifications, theme, privacy',
                        onTap: () {
                          Navigator.pop(context);
                          // TODO: Navigate to settings
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Settings coming soon!')),
                          );
                        },
                      ),
                      _buildMenuItem(
                        icon: Icons.help,
                        title: 'Help & Support',
                        subtitle: 'FAQ, report issues',
                        onTap: () {
                          Navigator.pop(context);
                          // TODO: Navigate to help
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(content: Text('Help & Support coming soon!')),
                          );
                        },
                      ),
                      _buildMenuItem(
                        icon: Icons.info,
                        title: 'About dekuMArt',
                        subtitle: 'Version 1.0.0',
                        onTap: () {
                          Navigator.pop(context);
                          _showAboutDialog(context);
                        },
                      ),
                    ],
                  ),
                ),
                
                // Logout button
                Container(
                  padding: const EdgeInsets.all(16),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: () async {
                        Navigator.pop(context);
                        await authProvider.signOut();
                      },
                      icon: const Icon(Icons.logout),
                      label: const Text('Logout'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMenuItem({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon, color: const Color(0xFF2E7D32)),
      title: Text(title),
      subtitle: Text(subtitle),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
    );
  }

  void _showAboutDialog(BuildContext context) {
    showAboutDialog(
      context: context,
      applicationName: 'dekuMArt',
      applicationVersion: '1.0.0',
      applicationIcon: const Icon(
        Icons.shopping_bag,
        color: Color(0xFF2E7D32),
        size: 48,
      ),
      children: [
        const Text(
          'A Flutter-based community marketplace app for Dedan Kimathi University students, faculty, and staff.',
        ),
        const SizedBox(height: 16),
        const Text(
          'Promoting campus sustainability through second-hand trading and fostering community engagement.',
        ),
      ],
    );
  }
}
